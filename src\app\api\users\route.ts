import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !anonKey) {
      return NextResponse.json({
        success: false,
        error: 'Environment variables not configured'
      }, { status: 500 });
    }

    // 解析查询参数
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    const username = searchParams.get('username');

    // 构建查询URL - 使用中文表名"用户资料"
    let queryUrl = `${supabaseUrl}/rest/v1/用户资料?select=id,账号,姓名,职称,部门,电话,微信,avatar_url,created_at,updated_at`;

    if (id) {
      queryUrl += `&id=eq.${id}`;
    } else if (username) {
      queryUrl += `&账号=eq.${username}`;
    } else {
      return NextResponse.json({
        success: false,
        error: 'ID or username is required'
      }, { status: 400 });
    }

    const response = await fetch(queryUrl, {
      headers: {
        'apikey': anonKey,
        'Authorization': `Bearer ${anonKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();
      const user = data.length > 0 ? data[0] : null;

      if (!user) {
        return NextResponse.json({
          success: false,
          error: 'User not found'
        }, { status: 404 });
      }

      // 映射中文字段到英文接口
      const mappedUser = {
        id: user.id,
        username: user.账号,
        name: user.姓名,
        position: user.职称 || '',
        department: user.部门 || '',
        phone: user.电话 || '',
        wechat: user.微信 || '',
        points: 0, // 暂无对应字段
        avatar_url: user.avatar_url,
        created_at: user.created_at,
        updated_at: user.updated_at
      };

      return NextResponse.json({ success: true, data: mappedUser });
    } else {
      const errorText = await response.text();
      console.error('Supabase error:', errorText);
      return NextResponse.json({ 
        success: false, 
        error: 'Query failed',
        details: errorText 
      }, { status: response.status });
    }
  } catch (error) {
    console.error('Get user error:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !anonKey) {
      return NextResponse.json({
        success: false,
        error: 'Environment variables not configured'
      }, { status: 500 });
    }

    const userData = await request.json();

    // 添加时间戳
    const userWithTimestamps = {
      ...userData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const response = await fetch(`${supabaseUrl}/rest/v1/users`, {
      method: 'POST',
      headers: {
        'apikey': anonKey,
        'Authorization': `Bearer ${anonKey}`,
        'Content-Type': 'application/json',
        'Prefer': 'return=representation'
      },
      body: JSON.stringify(userWithTimestamps)
    });

    if (response.ok) {
      const data = await response.json();
      return NextResponse.json({ success: true, data: data[0] });
    } else {
      const errorText = await response.text();
      console.error('Supabase error:', errorText);
      return NextResponse.json({ 
        success: false, 
        error: 'Create user failed',
        details: errorText 
      }, { status: response.status });
    }
  } catch (error) {
    console.error('Create user error:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const anonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !anonKey) {
      return NextResponse.json({
        success: false,
        error: 'Environment variables not configured'
      }, { status: 500 });
    }

    // 尝试使用anon key而不是service role key来避免JWT签名问题
    const authKey = anonKey; // 暂时只使用anon key
    console.log('🔑 [用户API] 使用认证密钥类型: Anon Key (避免JWT签名问题)');

    const { id, ...updateData } = await request.json();

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'User ID is required'
      }, { status: 400 });
    }

    // 映射英文字段到中文字段
    const chineseUpdateData: any = {
      updated_at: new Date().toISOString()
    };

    if (updateData.username) chineseUpdateData.账号 = updateData.username;
    if (updateData.name) chineseUpdateData.姓名 = updateData.name;
    if (updateData.position) chineseUpdateData.职称 = updateData.position;
    if (updateData.department) chineseUpdateData.部门 = updateData.department;
    if (updateData.phone) chineseUpdateData.电话 = updateData.phone;
    if (updateData.wechat) chineseUpdateData.微信 = updateData.wechat;
    if (updateData.avatar_url !== undefined) chineseUpdateData.avatar_url = updateData.avatar_url;

    console.log('🔄 [用户API] 准备更新用户，ID:', id);
    console.log('📝 [用户API] 更新数据:', chineseUpdateData);

    // 添加重试机制
    let response;
    let lastError;
    const maxRetries = 3;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 [用户API] 尝试第 ${attempt} 次请求...`);

        // 使用简化的认证头设置
        const headers: Record<string, string> = {
          'apikey': authKey,
          'Authorization': `Bearer ${authKey}`,
          'Content-Type': 'application/json',
          'Prefer': 'return=representation'
        };

        response = await fetch(`${supabaseUrl}/rest/v1/用户资料?id=eq.${id}`, {
          method: 'PATCH',
          headers,
          body: JSON.stringify(chineseUpdateData)
        });

        console.log('📡 [用户API] Supabase响应状态:', response.status);
        console.log('📡 [用户API] Supabase响应头:', Object.fromEntries(response.headers.entries()));

        // 如果请求成功，跳出重试循环
        break;

      } catch (error) {
        lastError = error;
        console.error(`❌ [用户API] 第 ${attempt} 次请求失败:`, error);

        if (attempt === maxRetries) {
          throw error; // 最后一次尝试失败，抛出错误
        }

        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }

    if (response.ok) {
      const data = await response.json();
      console.log('📊 [用户API] Supabase更新响应:', data);

      // 检查返回的数据
      if (!data || data.length === 0) {
        console.error('❌ [用户API] Supabase返回空数据');
        return NextResponse.json({
          success: false,
          error: 'No data returned from update operation'
        }, { status: 500 });
      }

      const updatedUser = data[0];
      console.log('✅ [用户API] 更新后的用户数据:', updatedUser);

      // 映射中文字段到英文接口
      const mappedUser = {
        id: updatedUser.id,
        username: updatedUser.账号,
        name: updatedUser.姓名,
        position: updatedUser.职称 || '',
        department: updatedUser.部门 || '',
        phone: updatedUser.电话 || '',
        wechat: updatedUser.微信 || '',
        points: 0, // 暂无对应字段
        avatar_url: updatedUser.avatar_url,
        created_at: updatedUser.created_at,
        updated_at: updatedUser.updated_at
      };

      console.log('🔄 [用户API] 映射后的用户数据:', mappedUser);
      return NextResponse.json({ success: true, data: mappedUser });
    } else {
      const errorText = await response.text();
      console.error('❌ [用户API] Supabase更新失败:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });

      // 特殊处理401认证错误
      if (response.status === 401) {
        console.error('🔐 [用户API] 认证失败，可能是JWT签名问题');
        return NextResponse.json({
          success: false,
          error: 'Authentication failed',
          message: '认证失败，请稍后重试或联系管理员',
          code: 'AUTH_FAILED',
          details: errorText
        }, { status: 401 });
      }

      return NextResponse.json({
        success: false,
        error: `Update user failed: ${response.status} ${response.statusText}`,
        details: errorText
      }, { status: response.status });
    }
  } catch (error) {
    console.error('❌ [用户API] 服务器错误:', error);
    return NextResponse.json({
      success: false,
      error: 'Server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
